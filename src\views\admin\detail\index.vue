<script setup>
import { useRoute, useRouter } from "vue-router";
import { triggerRef, computed, watch } from "vue";
import SimplyBreadcrumb from "@/views/components/SimplyBreadcrumb/index.vue";
import { UploadFilled, Close, Download } from "@element-plus/icons-vue";
import { ElMessage } from "element-plus";
import { getCosConfig } from "@/utils/cos";
import { getdate, randomString } from "@/utils/index";
import {
  complaintDetailApi,
  cityDistributionApi,
  complaintReplyApi,
  complaintReviewApi,
  addSuperviseApi,
  getDelayDetailApi,
  saveDistributionDraftApi,
  saveReplyDraftApi,
  saveReviewDraftApi,
  saveSuperviseDraftApi,
} from "@/apis/complaint";
import { useUserStore } from "@/store/modules/user";

import imageIcon from "@/assets/images/detail/image.png";
import wordIcon from "@/assets/images/detail/word.png";
import pdfIcon from "@/assets/images/detail/pdf.png";

// 获取cos配置
const cos = ref();
const cosConfig = ref({});
const getCosConfigFn = async () => {
  const res = await getCosConfig();
  if (res) {
    const { cosInstance, config } = res;
    cos.value = cosInstance;
    cosConfig.value = config;
  }
};

// 如果需要路由功能，可以取消注释
const route = useRoute();
const router = useRouter();
const userStore = useUserStore();

const type = route.query.type;

// tab
const activeTab = ref("1");
const handleTabChange = (tab) => {
  activeTab.value = tab.name;
};

const complaintData = ref({});

// 获取详情数据
const getDetail = async () => {
  try {
    const res = await complaintDetailApi(route.query.complaintId);
    if (res) {
      complaintData.value = res.data;

      if (complaintData.value.files) {
        complaintData.value.files.forEach((file) => {
          file.type = getFileType(file.name);
        });
      }
      if (complaintData.value.replyFiles) {
        complaintData.value.replyFiles.forEach((file) => {
          file.type = getFileType(file.name);
        });
      }
      if (complaintData.value.superviseFiles) {
        complaintData.value.superviseFiles.forEach((file) => {
          file.type = getFileType(file.name);
        });
      }
      if (complaintData.value.complaintLogs) {
        complaintData.value.complaintLogs.forEach((item) => {
          if (item.replyFiles) {
            item.replyFiles.forEach((file) => {
              file.type = getFileType(file.name);
            });
          }
        });
      }
      if (complaintData.value.complaintSuperviseLogs) {
        complaintData.value.complaintSuperviseLogs.forEach((item) => {
          if (item.replyFiles) {
            item.replyFiles.forEach((file) => {
              file.type = getFileType(file.name);
            });
          }
          if (item.superviseFiles) {
            item.superviseFiles.forEach((file) => {
              file.type = getFileType(file.name);
            });
          }
        });
      }
    }
  } catch (err) {
    console.log("获取详情错误", err);
  }
};

// 分办表单数据
const assignmentForm = ref({
  assignmentUnit: "",
  assignmentOpinion: "",
});

// 分办表单验证规则
const assignmentRules = {
  assignmentUnit: [
    { required: true, message: "请选择分办单位", trigger: "change" },
  ],
  assignmentOpinion: [
    { required: true, message: "请输入分办意见", trigger: "blur" },
    { max: 100, message: "分办意见不能超过100个字符", trigger: "blur" },
  ],
};

// 分办单位选项
const assignmentUnitOptions = [
  { label: "本级", value: "myself" },
  { label: "下级", value: "lower" },
];

// 审核表单数据
const reviewForm = ref({
  status: "",
  reason: "",
});

// 审核表单验证规则
const reviewRules = computed(() => ({
  status: [{ required: true, message: "请选择审核状态", trigger: "change" }],
  reason:
    reviewForm.value.status === "未通过"
      ? [
          { required: true, message: "请输入不通过原因", trigger: "blur" },
          { max: 50, message: "不通过原因不能超过50个字符", trigger: "blur" },
        ]
      : [],
}));

// 审核状态选项
const reviewStatusOptions = [
  { label: "通过", value: "1" },
  { label: "不通过", value: "2" },
];

// 办理单位选项 - 根据是否为省直属学校动态调整
const superviseByOptions = computed(() => {
  const baseOptions = [
    { label: "省级", value: "3" },
    { label: "地市", value: "2" },
    { label: "县区", value: "1" },
  ];

  // 如果是省直属学校，去除县区选项
  if (complaintData.value.areaTitle === "直属学校") {
    return baseOptions.filter((option) => option.value !== "1");
  }

  return baseOptions;
});

// 回复表单数据
const replyForm = ref({
  content: "",
  replyTitle: "",
  replyMobile: "",
  attachments: [],
  superviseBy: "3", // 办理单位
});

// 表单验证规则
const replyRules = computed(() => {
  const rules = {
    superviseBy: [
      { required: true, message: "请选择办理单位", trigger: "change" },
    ],
    content: [
      {
        required: true,
        message: type === "add-supervise" ? "请输入督办意见" : "请输入回复内容",
        trigger: "blur",
      },
    ],
    replyTitle: [
      { required: true, message: "请输入经办人姓名", trigger: "blur" },
    ],
    replyMobile: [
      { required: true, message: "请输入经办人电话", trigger: "blur" },
      {
        pattern: /^1[3-9]\d{9}$/,
        message: "请输入正确的手机号码",
        trigger: "blur",
      },
    ],
  };

  // 如果是督办意见，添加办理单位验证
  if (type === "add-supervise") {
    rules.superviseBy = [
      { required: true, message: "请选择办理单位", trigger: "change" },
    ];
  }

  return rules;
});

// 文件上传相关
const fileInputRef = ref();
const fileList = ref([]);
const isDragOver = ref(false);
// 添加一个强制更新的标志
const forceUpdate = ref(0);

// 支持的文件格式
const allowedFormats = ["jpg", "jpeg", "png", "doc", "docx", "pdf"];

// 文件验证
const validateFile = (file) => {
  const fileExtension = file.name.split(".").pop().toLowerCase();

  if (!allowedFormats.includes(fileExtension)) {
    ElMessage.error("只支持 jpg/doc/docx/pdf/png 格式的文件");
    return false;
  }

  if (fileList.value.length >= 5) {
    ElMessage.error("最多只能上传5个文件");
    return false;
  }

  const isLt10M = file.size / 1024 / 1024 < 50;
  if (!isLt10M) {
    ElMessage.error("文件大小不能超过 50MB");
    return false;
  }

  return true;
};

// 处理文件选择
const handleFileSelect = async (files) => {
  const validFiles = Array.from(files).filter(validateFile);

  for (const file of validFiles) {
    const fileItem = {
      id: Date.now() + Math.random(),
      name: file.name,
      size: file.size,
      type: getFileType(file.name),
      file: file,
      url: URL.createObjectURL(file),
      uploading: true,
      cosUrl: "",
    };

    fileList.value.push(fileItem);

    // 上传到COS
    try {
      const cosUrl = await uploadFileToCos(file);

      // 找到对应的文件项并更新状态
      const index = fileList.value.findIndex((item) => item.id === fileItem.id);
      if (index > -1) {
        // 创建新的文件对象来确保响应式更新
        const updatedFile = {
          ...fileList.value[index],
          cosUrl: cosUrl,
          uploading: false,
        };

        // 使用 splice 替换整个对象来强制触发响应式更新
        fileList.value.splice(index, 1, updatedFile);

        // 强制触发响应式更新
        triggerRef(fileList);

        // 触发强制更新
        forceUpdate.value++;

        // 使用 nextTick 确保 DOM 更新
        await nextTick();

        // 添加调试信息
        console.log("文件上传完成，状态已更新:", {
          fileName: file.name,
          uploading: updatedFile.uploading,
          cosUrl: updatedFile.cosUrl,
          forceUpdate: forceUpdate.value,
        });
      }
    } catch (error) {
      console.error("文件上传失败:", error);
      ElMessage.error(`文件 ${file.name} 上传失败`);
      // 移除上传失败的文件
      const index = fileList.value.findIndex((item) => item.id === fileItem.id);
      if (index > -1) {
        fileList.value.splice(index, 1);
      }
    }
  }

  replyForm.value.attachments = [...fileList.value];
};

// 上传文件到COS
const uploadFileToCos = (file) => {
  return new Promise((resolve, reject) => {
    if (!cosConfig.value) {
      reject(new Error("COS配置错误"));
      return;
    }

    const index = file.name.lastIndexOf(".");
    const type = file.name.substr(index); // 截取文件后缀
    const key =
      "jssjytzztspt/" +
      getdate() +
      randomString(
        32,
        "0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ"
      ) +
      type;

    cos.value.putObject(
      {
        Bucket: cosConfig.value.bucket,
        Region: cosConfig.value.region,
        Key: key,
        StorageClass: "STANDARD",
        Body: file,
      },
      (err, data) => {
        if (err) {
          console.log("upload-error", err);
          reject(err);
          return;
        }
        if (data && data.statusCode == 200) {
          const cosUrl = "https://" + data.Location;
          resolve(cosUrl);
        } else {
          reject(new Error("上传失败"));
        }
      }
    );
  });
};

// 点击上传区域
const handleUploadClick = () => {
  fileInputRef.value?.click();
};

// 文件输入变化
const handleFileInputChange = (event) => {
  const files = event.target.files;
  if (files && files.length > 0) {
    handleFileSelect(files);
  }
  // 清空input值，允许重复选择同一文件
  event.target.value = "";
};

// 拖拽相关事件
const handleDragOver = (event) => {
  event.preventDefault();
  isDragOver.value = true;
};

const handleDragLeave = (event) => {
  event.preventDefault();
  isDragOver.value = false;
};

const handleDrop = (event) => {
  event.preventDefault();
  isDragOver.value = false;

  const files = event.dataTransfer.files;
  if (files && files.length > 0) {
    handleFileSelect(files);
  }
};

// 删除文件
const removeFile = (fileId) => {
  const index = fileList.value.findIndex((file) => file.id === fileId);
  if (index > -1) {
    // 释放URL对象
    if (fileList.value[index].url.startsWith("blob:")) {
      URL.revokeObjectURL(fileList.value[index].url);
    }
    fileList.value.splice(index, 1);
    replyForm.value.attachments = [...fileList.value];
  }
};

// 获取文件类型
const getFileType = (fileName) => {
  const extension = fileName.split(".").pop().toLowerCase();
  if (["jpg", "jpeg", "png", "gif"].includes(extension)) {
    return "image";
  } else if (["pdf"].includes(extension)) {
    return "pdf";
  } else if (["doc", "docx"].includes(extension)) {
    return "word";
  }
  return "word"; // 默认类型
};

// 格式化文件大小
const formatFileSize = (bytes) => {
  if (bytes === 0) return "0 B";
  const k = 1024;
  const sizes = ["B", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
};

// 提交回复
const submitReply = async () => {
  await replyFormRef.value.validate();

  // 等待所有文件上传完成
  const uploadingFiles = fileList.value.filter((file) => file.uploading);
  if (uploadingFiles.length > 0) {
    ElMessage.warning("文件正在上传中，请稍候...");
    return;
  }

  // 组织文件数据为name和url组成的数组对象
  const replyFiles = fileList.value.map((file) => ({
    name: file.name,
    url: file.cosUrl,
  }));

  const params = {
    complaintId: route.query.complaintId,
    replyContent: replyForm.value.content,
    replyFiles: replyFiles,
    replyMobile: replyForm.value.replyMobile || "",
    replyTitle: replyForm.value.replyTitle || "",
  };

  await complaintReplyApi(params);
  ElMessage.success("回复提交成功");

  // 重置表单
  replyForm.value = {
    content: "",
    replyTitle: "",
    replyMobile: "",
    attachments: [],
  };

  // 清理文件列表和URL对象
  fileList.value.forEach((file) => {
    if (file.url.startsWith("blob:")) {
      URL.revokeObjectURL(file.url);
    }
  });
  fileList.value = [];

  // 返回上一页
  router.back();
};

// 统一保存函数
const handleSave = async () => {
  let res, params;
  if (type === "distribution") {
    params = {
      myself: assignmentForm.value.assignmentUnit === "myself", // 是否为本级处理
      complaintId: route.query.complaintId, // 当前投诉id
      distributeOpinion: assignmentForm.value.assignmentOpinion, // 分办意见
    };
    res = await saveDistributionDraftApi(params);
  } else if (type === "review") {
    params = {
      complaintId: route.query.complaintId,
      approveStatus: reviewForm.value.status, // 状态
      approveOpinion: reviewForm.value.reason, // 审核意见
    };

    res = await saveReviewDraftApi(params);
  } else if (type === "reply") {
    params = {
      complaintId: route.query.complaintId,
      replyContent: replyForm.value.content,
      replyFiles: replyFiles,
      replyMobile: replyForm.value.replyMobile || "",
      replyTitle: replyForm.value.replyTitle || "",
    };

    res = await saveReplyDraftApi(params);
  } else if (type === "add-supervise") {
    params = {
      complaintId: route.query.complaintId,
      superviseBy:
        type === "add-supervise" && !complaintData.value.superviseOpinion
          ? replyForm.value.superviseBy
          : complaintData.value?.complaintSuperviseLogs?.length > 0
          ? complaintData.value?.complaintSuperviseLogs?.[0]?.superviseOrgId
          : "",
      superviseOpinion: replyForm.value.content,
      superviseFiles: superviseFiles,
    };
    res = await saveSuperviseDraftApi(replyForm.value);
  }
  if (res) {
    ElMessage.success("草稿保存成功");
  }
};

// 统一提交函数
const handleSubmit = async () => {
  try {
    // 根据type和用户角色决定调用哪个接口
    switch (type) {
      case "distribution":
        await submitAssignment();
        break;
      case "review":
        await submitReview();
        break;
      case "reply":
        await submitReply();
        break;
      case "add-supervise":
        await submitSupervise();
        break;
      default:
        ElMessage.error("未知操作类型");
    }
  } catch (error) {
    console.error("提交失败:", error);
  }
};

// 表单引用
const assignmentFormRef = ref();
const reviewFormRef = ref();
const replyFormRef = ref();

// 提交分办
const submitAssignment = async () => {
  await assignmentFormRef.value.validate();

  const params = {
    myself: assignmentForm.value.assignmentUnit === "myself", // 是否为本级处理
    complaintId: route.query.complaintId, // 当前投诉id
    distributeOpinion: assignmentForm.value.assignmentOpinion, // 分办意见
  };

  await cityDistributionApi(params);
  ElMessage.success("分办提交成功");

  // 重置表单
  assignmentForm.value = {
    assignmentUnit: "",
    assignmentOpinion: "",
  };

  // 返回上一页
  router.back();
};

// 审核状态变化处理
const handleReviewStatusChange = (value) => {
  if (value === "已通过") {
    // 如果选择通过，清空不通过原因
    reviewForm.value.reason = "";
  }
};

// 提交审核
const submitReview = async () => {
  await reviewFormRef.value.validate();

  const params = {
    complaintId: route.query.complaintId,
    approveStatus: reviewForm.value.status, // 状态
    approveOpinion: reviewForm.value.reason, // 审核意见
  };

  await complaintReviewApi(params);
  ElMessage.success("审核提交成功");

  // 重置表单
  reviewForm.value = {
    status: "",
    reason: "",
  };

  // 返回上一页
  router.back();
};

// 提交督办意见
const submitSupervise = async () => {
  await replyFormRef.value.validate();

  // 等待所有文件上传完成
  const uploadingFiles = fileList.value.filter((file) => file.uploading);
  if (uploadingFiles.length > 0) {
    ElMessage.warning("文件正在上传中，请稍候...");
    return;
  }

  // 组织文件数据为name和url组成的数组对象
  const superviseFiles = fileList.value.map((file) => ({
    name: file.name,
    url: file.cosUrl,
  }));

  const params = {
    complaintId: route.query.complaintId,
    superviseBy:
      type === "add-supervise" && !complaintData.value.superviseOpinion
        ? replyForm.value.superviseBy
        : complaintData.value?.complaintSuperviseLogs?.length > 0
        ? complaintData.value?.complaintSuperviseLogs?.[0]?.superviseOrgId
        : "",
    superviseOpinion: replyForm.value.content,
    superviseFiles: superviseFiles,
  };

  await addSuperviseApi(params);
  ElMessage.success("督办意见提交成功");

  // 重置表单
  replyForm.value = {
    content: "",
    replyTitle: "",
    replyMobile: "",
    attachments: [],
    superviseBy: "",
  };

  // 清理文件列表和URL对象
  fileList.value.forEach((file) => {
    if (file.url.startsWith("blob:")) {
      URL.revokeObjectURL(file.url);
    }
  });
  fileList.value = [];

  // 返回上一页
  router.back();
};

// 下载附件
const downloadAttachment = (attachment) => {
  console.log("下载附件:", attachment.name);
  // 创建一个临时的a标签来触发下载
  const link = document.createElement("a");
  // 优先使用 url 字段，如果没有则使用 cosUrl 字段
  link.href = attachment.url || attachment.cosUrl;
  link.download = attachment.name;
  link.target = "_blank";
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};

// 获取时限显示文本和样式类
const getTimeLimitInfo = (timeLimit, status) => {
  if (status === "已办结") {
    return {
      text: "已完成",
      class: "time-limit-completed",
    };
  }

  const remainingDays = timeLimit;

  if (remainingDays < 0) {
    return {
      text: `超时${Math.abs(remainingDays)}天`,
      class: "time-limit-overdue",
    };
  }

  let className = "";
  if (remainingDays >= 1 && remainingDays < 3) {
    className = "time-limit-urgent";
  } else if (remainingDays >= 3 && remainingDays <= 5) {
    className = "time-limit-warning";
  } else if (remainingDays > 5) {
    className = "time-limit-normal";
  }

  return {
    text: `剩余${remainingDays}天`,
    class: className,
  };
};

// 获取状态CSS类名
const getStatusClass = (status) => {
  const classMap = {
    待分办: "status-pending-assignment",
    待处理: "status-pending-processing",
    待审核: "status-pending-review",
    审核未通过: "status-review-failed",
    已办结: "status-completed",
    "待处理（省厅督办件）": "status-pending-provincial",
    "已办结（省厅督办件）": "status-completed-provincial",
  };
  return classMap[status] || "status-default";
};

// 延期弹窗相关变量
const extensionDialogVisible = ref(false);
const extensionFormRef = ref();
const extensionForm = ref({
  days: 1,
  attachments: [],
});

// 弹窗模式：'apply' 申请延期，'view' 查看延期
const dialogMode = ref("view");

// 延期文件列表
const extensionFileList = ref([]);

// 查看延期信息
const handleViewExtension = async () => {
  try {
    dialogMode.value = "view";
    extensionDialogVisible.value = true;

    // 调用API获取延期详情
    const { data } = await getDelayDetailApi(complaintData.value.complaintId);

    // 回填延期时间
    extensionForm.value = {
      days: data.delay || 0,
      attachments: [],
    };

    // 回填附件数据
    if (data.delayFiles && Array.isArray(data.delayFiles)) {
      extensionFileList.value = data.delayFiles.map((file, index) => ({
        id: index + 1,
        name: file.name,
        type: getFileType(file.name),
        url: file.url,
        cosUrl: file.url,
        uploading: false,
      }));
    } else {
      extensionFileList.value = [];
    }
  } catch (error) {
    console.error("获取延期详情失败:", error);
    extensionDialogVisible.value = false;
  }
};

// 关闭延期弹窗
const handleCloseExtensionDialog = () => {
  extensionDialogVisible.value = false;
  extensionFileList.value = [];
};

// 下载延期附件
const downloadExtensionAttachment = (file) => {
  console.log("下载附件:", file.name);
  // 创建一个临时的a标签来触发下载
  const link = document.createElement("a");
  link.href = file.cosUrl;
  link.download = file.name;
  link.target = "_blank";
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};

onMounted(async () => {
  await getCosConfigFn();
  getDetail();
});
</script>

<template>
  <div class="detail app-container">
    <SimplyBreadcrumb />
    <section class="content px-[24px] py-[20px] bg-[#fff] rounded-[10px]">
      <div class="content-header flex-between-center mb-[12px]">
        <div class="header-title">{{ route.meta.title }}</div>
        <div class="header-right flex items-center gap-[12px]">
          <!-- 时限显示 -->
          <div class="time-limit-display flex items-center gap-[8px]">
            <span
              :class="[
                'time-limit-tag',
                getTimeLimitInfo(complaintData.days || 5, complaintData.status)
                  .class,
              ]"
            >
              {{
                getTimeLimitInfo(complaintData.days || 5, complaintData.status)
                  .text
              }}
            </span>
            <div
              v-if="complaintData.delay"
              class="px-[8px] py-[2px] bg-blue-100 text-blue-600 rounded-[8px] text-[13px] cursor-pointer hover:bg-blue-200 transition-colors"
              @click="handleViewExtension"
            >
              延时{{ complaintData.delay || 1 }}天
            </div>
          </div>
          <!-- 状态显示 -->
          <span
            :class="[
              'status-tag',
              getStatusClass(complaintData.status || '待处理'),
            ]"
          >
            {{ complaintData.status || "待处理" }}
          </span>
        </div>
      </div>
      <div class="tab-container">
        <el-tabs v-model="activeTab" @tab-click="handleTabChange">
          <el-tab-pane label="投诉信息" name="1"></el-tab-pane>
          <el-tab-pane label="审核记录" name="2"></el-tab-pane>
          <el-tab-pane label="督办记录" name="3"></el-tab-pane>
        </el-tabs>
      </div>
      <!-- 投诉人信息 -->
      <div class="info-section mb-[20px]" v-if="activeTab === '1'">
        <div class="section-title">
          <img
            src="@/assets/images/detail/person.png"
            alt=""
            width="12"
            height="14"
          />
          <span class="title-text">投诉人信息</span>
        </div>
        <div class="info-grid">
          <div class="info-item">
            <span class="label">姓名</span>
            <span class="value">{{ complaintData.userTitle }}</span>
          </div>
          <div class="info-item">
            <span class="label">手机</span>
            <span class="value">{{ complaintData.userMobile }}</span>
          </div>
          <div class="info-item">
            <span class="label">身份</span>
            <span class="value">{{ complaintData.identity }}</span>
          </div>
          <div class="info-item">
            <span class="label">性别</span>
            <span class="value">{{
              complaintData.sex === 1
                ? "男"
                : complaintData.sex === 2
                ? "女"
                : "-"
            }}</span>
          </div>
        </div>
      </div>

      <!-- 投诉学校信息 -->
      <div class="info-section mb-[20px]" v-if="activeTab === '1'">
        <div class="section-title">
          <img
            src="@/assets/images/detail/school.png"
            alt=""
            width="12"
            height="14"
          />
          <span class="title-text">投诉学校信息</span>
        </div>
        <div class="info-grid grid-3">
          <div class="info-item">
            <span class="label">地区</span>
            <span class="value">{{
              `江苏省${complaintData.cityTitle}${
                complaintData.areaTitle === "直属学校"
                  ? ""
                  : complaintData.areaTitle
              }`
            }}</span>
          </div>
          <div class="info-item">
            <span class="label">学校</span>
            <span class="value">{{ complaintData.schoolTitle }}</span>
          </div>
          <div class="info-item">
            <span class="label">学校类型</span>
            <span class="value">{{ complaintData.stageTitle }}</span>
          </div>
        </div>
      </div>

      <!-- 具体问题 -->
      <div class="info-section mb-[20px]" v-if="activeTab === '1'">
        <div class="section-title">
          <img
            src="@/assets/images/detail/question.png"
            alt=""
            width="12"
            height="12"
          />
          <span class="title-text">具体问题</span>
        </div>
        <div class="problem-content">
          <div class="info-grid grid-2 mb-[20px]">
            <div class="info-item">
              <span class="label">投诉时间</span>
              <span class="value">{{ complaintData.createAt }}</span>
            </div>
            <div class="info-item">
              <span class="label">分类</span>
              <span class="value">{{ complaintData.complaintTypeTitle }}</span>
            </div>
          </div>

          <div class="problem-description mb-[20px]">
            <div class="description-text">
              <div class="label mb-[8px]">举报内容</div>
              {{ complaintData.content }}
            </div>
          </div>

          <div class="attachments">
            <div class="label mb-[12px]">问题附件</div>
            <div class="attachment-list">
              <div
                v-for="(attachment, index) in complaintData?.files"
                :key="index"
                class="attachment-item"
                @click="downloadAttachment(attachment)"
              >
                <img
                  class="attachment-icon"
                  :src="
                    attachment.type === 'img'
                      ? imageIcon
                      : attachment.type === 'pdf'
                      ? pdfIcon
                      : attachment.type === 'word' || attachment.type === 'doc'
                      ? wordIcon
                      : ''
                  "
                />
                <span class="attachment-name">{{ attachment.name }}</span>
                <img
                  class="download-icon"
                  src="@/assets/images/detail/download.png"
                  width="15"
                  height="14"
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 分办信息 -->
      <div
        class="info-section mb-[20px]"
        v-if="
          complaintData?.distributeOpinion &&
          (activeTab === '1' || activeTab === '2')
        "
      >
        <div class="section-title">
          <img
            src="@/assets/images/detail/supervise-info.png"
            alt=""
            width="14"
            height="14"
          />
          <span class="title-text">分办信息</span>
        </div>
        <div class="assignment-content">
          <div class="assignment-description">
            <div class="label mb-[8px]">分办意见</div>
            <div class="description-text">
              {{ complaintData?.distributeOpinion }}
            </div>
          </div>
        </div>
      </div>

      <!-- 最新回复 -->
      <div class="info-section mb-[20px]" v-if="activeTab === '1'">
        <div class="section-title">
          <img
            src="@/assets/images/detail/review-answer.png"
            alt=""
            width="14"
            height="14"
          />
          <span class="title-text">{{
            type === "review" &&
            complaintData.approveStatus &&
            complaintData.complaintLogs?.length > 1
              ? "审核回复"
              : complaintData.approveStatus === 2
              ? "最新审核回复"
              : "最新回复"
          }}</span>
        </div>
        <div class="reply-content">
          <!-- 两行两列布局：办理单位、回复时间、经办人姓名、经办人电话 -->
          <div class="info-grid grid-2 mb-[20px]">
            <div class="info-item no-padding-bg">
              <span class="label">办理单位</span>
              <span class="value" v-if="!complaintData.superviseOpinion">{{
                type === "review" && complaintData.complaintLogs?.length > 1
                  ? complaintData?.complaintLogs[1]?.replyOrganization
                  : complaintData?.replyOrganization
              }}</span>
              <span class="value" v-else>
                {{ complaintData?.complaintLogs?.[0]?.replyOrganization }}
              </span>
            </div>
            <div class="info-item no-padding-bg">
              <span class="label">回复时间</span>
              <span class="value" v-if="!complaintData.superviseOpinion">{{
                type === "review" && complaintData.complaintLogs?.length > 1
                  ? complaintData?.complaintLogs[1]?.replyAt
                  : complaintData?.replyAt
              }}</span>
              <span class="value" v-else>{{
                complaintData?.complaintLogs?.[0]?.replyAt
              }}</span>
            </div>
            <div class="info-item no-padding-bg">
              <span class="label">经办人姓名</span>
              <span class="value" v-if="!complaintData.superviseOpinion">{{
                type === "review" && complaintData.complaintLogs?.length > 1
                  ? complaintData?.complaintLogs[1]?.replyTitle
                  : complaintData?.replyTitle
              }}</span>
              <span class="value" v-else>
                {{ complaintData?.complaintLogs[0]?.replyTitle }}
              </span>
            </div>
            <div class="info-item no-padding-bg">
              <span class="label">经办人电话</span>
              <span class="value" v-if="!complaintData.superviseOpinion">{{
                type === "review" && complaintData.complaintLogs?.length > 1
                  ? complaintData?.complaintLogs[1]?.replyMobile
                  : complaintData?.replyMobile
              }}</span>
              <span class="value" v-else>
                {{ complaintData?.complaintLogs[0]?.replyMobile }}
              </span>
            </div>
          </div>

          <!-- 回复内容单独一行 -->
          <div class="reply-description mb-[20px]">
            <div class="label mb-[8px]">回复内容</div>
            <div
              class="description-text no-padding-bg"
              v-if="!complaintData.superviseOpinion"
            >
              {{
                type === "review" && complaintData.complaintLogs?.length > 1
                  ? complaintData?.complaintLogs[1]?.replyContent
                  : complaintData?.replyContent
              }}
            </div>
            <div class="description-text no-padding-bg" v-else>
              {{ complaintData?.complaintLogs?.[0]?.replyContent }}
            </div>
          </div>

          <!-- 回复附件单独一行 -->
          <div
            class="attachments"
            v-if="
              !complaintData.superviseOpinion &&
              ((complaintData.complaintLogs?.length === 1 &&
                complaintData?.replyFiles &&
                complaintData?.replyFiles.length > 0) ||
                type === 'reply')
            "
          >
            <div class="label mb-[12px]">回复附件</div>
            <div class="attachment-list">
              <div
                v-for="(attachment, attachIndex) in complaintData?.replyFiles"
                :key="attachIndex"
                class="attachment-item"
                @click="downloadAttachment(attachment)"
              >
                <img
                  class="attachment-icon"
                  :src="
                    attachment?.type === 'image'
                      ? imageIcon
                      : attachment?.type === 'pdf'
                      ? pdfIcon
                      : attachment?.type === 'word'
                      ? wordIcon
                      : ''
                  "
                />
                <span class="attachment-name">{{ attachment?.name }}</span>
                <img
                  class="download-icon"
                  src="@/assets/images/detail/download.png"
                  width="15"
                  height="14"
                />
              </div>
            </div>
          </div>
          <div
            class="attachments"
            v-if="
              !complaintData.superviseOpinion &&
              type === 'review' &&
              complaintData.complaintLogs?.length > 1 &&
              complaintData?.complaintLogs?.[1]?.replyFiles &&
              complaintData?.complaintLogs?.[1]?.replyFiles.length > 0
            "
          >
            <div class="label mb-[12px]">回复附件</div>
            <div class="attachment-list">
              <div
                v-for="(attachment, attachIndex) in complaintData
                  ?.complaintLogs?.[1]?.replyFiles"
                :key="attachIndex"
                class="attachment-item"
                @click="downloadAttachment(attachment)"
              >
                <img
                  class="attachment-icon"
                  :src="
                    attachment?.type === 'image'
                      ? imageIcon
                      : attachment?.type === 'pdf'
                      ? pdfIcon
                      : attachment?.type === 'word'
                      ? wordIcon
                      : ''
                  "
                />
                <span class="attachment-name">{{ attachment?.name }}</span>
                <img
                  class="download-icon"
                  src="@/assets/images/detail/download.png"
                  width="15"
                  height="14"
                />
              </div>
            </div>
          </div>
          <div
            class="attachments"
            v-if="
              complaintData.superviseOpinion &&
              complaintData?.complaintLogs?.[0]?.replyFiles &&
              complaintData?.complaintLogs?.[0]?.replyFiles.length > 0
            "
          >
            <div class="label mb-[12px]">回复附件</div>
            <div class="attachment-list">
              <div
                v-for="(attachment, attachIndex) in complaintData
                  ?.complaintLogs?.[0]?.replyFiles"
                :key="attachIndex"
                class="attachment-item"
                @click="downloadAttachment(attachment)"
              >
                <img
                  class="attachment-icon"
                  :src="
                    attachment?.type === 'image'
                      ? imageIcon
                      : attachment?.type === 'pdf'
                      ? pdfIcon
                      : attachment?.type === 'word'
                      ? wordIcon
                      : ''
                  "
                />
                <span class="attachment-name">{{ attachment?.name }}</span>
                <img
                  class="download-icon"
                  src="@/assets/images/detail/download.png"
                  width="15"
                  height="14"
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 审核相关 -->
      <div
        class="info-section mb-[20px]"
        v-if="!complaintData.superviseOpinion && activeTab === '1'"
      >
        <div class="section-title">
          <img
            src="@/assets/images/detail/review-info.png"
            alt=""
            width="14"
            height="14"
          />
          <span class="title-text">最新审核信息</span>
        </div>
        <div class="review-content">
          <div class="review-status mb-[5px]">
            <span
              class="status-label"
              :class="
                complaintData.approveStatus === 1
                  ? 'status-passed'
                  : 'status-failed'
              "
              >{{
                complaintData.approveStatus === 1 ? "已通过" : "未通过"
              }}</span
            >
            <span class="review-time">{{ complaintData.approveAt }}</span>
          </div>

          <div class="review-description" v-if="complaintData.approveOpinion">
            <div class="description-text">
              {{ complaintData.approveOpinion }}
            </div>
          </div>
        </div>
      </div>

      <!-- 最新审核回复 （多次审核最新的一个回复）用于审核 -->
      <div
        class="info-section mb-[20px]"
        v-if="
          type === 'review' &&
          activeTab === '1' &&
          complaintData?.complaintLogs?.length > 1
        "
      >
        <div class="section-title">
          <img
            src="@/assets/images/detail/review-answer.png"
            alt=""
            width="14"
            height="14"
          />
          <span class="title-text">最新审核回复</span>
        </div>
        <div class="reply-content">
          <!-- 两行两列布局：办理单位、回复时间、经办人姓名、经办人电话 -->
          <div class="info-grid grid-2 mb-[20px]">
            <div class="info-item no-padding-bg">
              <span class="label">办理单位</span>
              <span class="value">{{ complaintData?.replyOrganization }}</span>
            </div>
            <div class="info-item no-padding-bg">
              <span class="label">回复时间</span>
              <span class="value">{{ complaintData?.replyAt }}</span>
            </div>
            <div class="info-item no-padding-bg">
              <span class="label">经办人姓名</span>
              <span class="value">{{ complaintData?.replyTitle }}</span>
            </div>
            <div class="info-item no-padding-bg">
              <span class="label">经办人电话</span>
              <span class="value">{{ complaintData?.replyMobile }}</span>
            </div>
          </div>

          <!-- 回复内容单独一行 -->
          <div class="reply-description mb-[20px]">
            <div class="label mb-[8px]">回复内容</div>
            <div class="description-text no-padding-bg">
              {{ complaintData?.replyContent }}
            </div>
          </div>

          <!-- 回复附件单独一行 -->
          <div
            class="attachments"
            v-if="
              complaintData?.replyFiles && complaintData?.replyFiles.length > 0
            "
          >
            <div class="label mb-[12px]">回复附件</div>
            <div class="attachment-list">
              <div
                v-for="(attachment, attachIndex) in complaintData?.replyFiles"
                :key="attachIndex"
                class="attachment-item"
                @click="downloadAttachment(attachment)"
              >
                <img
                  class="attachment-icon"
                  :src="
                    attachment?.type === 'image'
                      ? imageIcon
                      : attachment?.type === 'pdf'
                      ? pdfIcon
                      : attachment?.type === 'word'
                      ? wordIcon
                      : ''
                  "
                />
                <span class="attachment-name">{{ attachment?.name }}</span>
                <img
                  class="download-icon"
                  src="@/assets/images/detail/download.png"
                  width="15"
                  height="14"
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 最新督办意见  -->
      <div
        class="info-section mb-[20px]"
        v-if="complaintData.superviseOpinion && activeTab === '1'"
      >
        <div class="section-title">
          <img
            src="@/assets/images/detail/supervise-info.png"
            alt=""
            width="14"
            height="14"
          />
          <span class="title-text">最新督办意见</span>
        </div>
        <div class="reply-content">
          <div class="reply-description mb-[20px]">
            <div class="label mb-[8px]">
              <span>督办意见</span>
              <span class="ml-[16px]">{{ complaintData?.superviseAt }}</span>
            </div>
            <div class="description-text no-padding-bg">
              {{ complaintData?.superviseOpinion }}
            </div>
          </div>
          <div
            class="attachments"
            v-if="
              complaintData?.superviseFiles &&
              complaintData?.superviseFiles.length > 0
            "
          >
            <div class="label mb-[12px]">回复附件</div>
            <div class="attachment-list">
              <div
                v-for="(
                  attachment, attachIndex
                ) in complaintData?.superviseFiles"
                :key="attachIndex"
                class="attachment-item"
                @click="downloadAttachment(attachment)"
              >
                <img
                  class="attachment-icon"
                  :src="
                    attachment?.type === 'image'
                      ? imageIcon
                      : attachment?.type === 'pdf'
                      ? pdfIcon
                      : attachment?.type === 'word'
                      ? wordIcon
                      : ''
                  "
                />
                <span class="attachment-name">{{ attachment?.name }}</span>
                <img
                  class="download-icon"
                  src="@/assets/images/detail/download.png"
                  width="15"
                  height="14"
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 最新督办回复 -->
      <div
        class="info-section mb-[20px]"
        v-if="complaintData.superviseOpinion && activeTab === '1'"
      >
        <div class="section-title">
          <img
            src="@/assets/images/detail/review-answer.png"
            alt=""
            width="14"
            height="14"
          />
          <span class="title-text">最新督办回复</span>
        </div>
        <div class="reply-content">
          <!-- 两行两列布局：办理单位、回复时间、经办人姓名、经办人电话 -->
          <div class="info-grid grid-2 mb-[20px]">
            <div class="info-item no-padding-bg">
              <span class="label">办理单位</span>
              <span class="value">{{ complaintData.replyOrganization }}</span>
            </div>
            <div class="info-item no-padding-bg">
              <span class="label">回复时间</span>
              <span class="value">{{ complaintData?.replyAt }}</span>
            </div>
            <div class="info-item no-padding-bg">
              <span class="label">经办人姓名</span>
              <span class="value">{{ complaintData?.replyTitle }}</span>
            </div>
            <div class="info-item no-padding-bg">
              <span class="label">经办人电话</span>
              <span class="value">{{ complaintData?.replyMobile }}</span>
            </div>
          </div>

          <!-- 回复内容单独一行 -->
          <div class="reply-description mb-[20px]">
            <div class="label mb-[8px]">回复内容</div>
            <div class="description-text no-padding-bg">
              {{ complaintData?.replyContent }}
            </div>
          </div>

          <!-- 回复附件单独一行 -->
          <div
            class="attachments"
            v-if="
              complaintData?.replyFiles && complaintData?.replyFiles.length > 0
            "
          >
            <div class="label mb-[12px]">回复附件</div>
            <div class="attachment-list">
              <div
                v-for="(attachment, attachIndex) in complaintData?.replyFiles"
                :key="attachIndex"
                class="attachment-item"
                @click="downloadAttachment(attachment)"
              >
                <img
                  class="attachment-icon"
                  :src="
                    attachment?.type === 'image'
                      ? imageIcon
                      : attachment?.type === 'pdf'
                      ? pdfIcon
                      : attachment?.type === 'word'
                      ? wordIcon
                      : ''
                  "
                />
                <span class="attachment-name">{{ attachment?.name }}</span>
                <img
                  class="download-icon"
                  src="@/assets/images/detail/download.png"
                  width="15"
                  height="14"
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 审核记录 -->
      <template v-if="activeTab === '2'">
        <template
          v-for="(item, index) in complaintData?.complaintLogs"
          :key="index"
        >
          <div class="info-section mb-[20px]">
            <div class="section-title">
              <img
                src="@/assets/images/detail/answer-info.png"
                alt=""
                width="14"
                height="11"
              />
              <span class="title-text">回复信息</span>
            </div>
            <div class="reply-content">
              <!-- 两行两列布局：办理单位、回复时间、经办人姓名、经办人电话 -->
              <div class="info-grid grid-2 mb-[20px]">
                <div class="info-item no-padding-bg">
                  <span class="label">办理单位</span>
                  <span class="value">{{ item?.replyOrganization }}</span>
                </div>
                <div class="info-item no-padding-bg">
                  <span class="label">回复时间</span>
                  <span class="value">{{ item?.replyAt }}</span>
                </div>
                <div class="info-item no-padding-bg">
                  <span class="label">经办人姓名</span>
                  <span class="value">{{ item?.replyTitle }}</span>
                </div>
                <div class="info-item no-padding-bg">
                  <span class="label">经办人电话</span>
                  <span class="value">{{ item?.replyMobile }}</span>
                </div>
              </div>

              <!-- 回复内容单独一行 -->
              <div class="reply-description mb-[20px]">
                <div class="label mb-[8px]">回复内容</div>
                <div class="description-text no-padding-bg">
                  {{ item?.replyContent }}
                </div>
              </div>

              <!-- 回复附件单独一行 -->
              <div
                class="attachments"
                v-if="item?.replyFiles && item?.replyFiles.length > 0"
              >
                <div class="label mb-[12px]">回复附件</div>
                <div class="attachment-list">
                  <div
                    v-for="(attachment, attachIndex) in item.replyFiles"
                    :key="attachIndex"
                    class="attachment-item"
                    @click="downloadAttachment(attachment)"
                  >
                    <img
                      class="attachment-icon"
                      :src="
                        attachment?.type === 'image'
                          ? imageIcon
                          : attachment?.type === 'pdf'
                          ? pdfIcon
                          : attachment?.type === 'word'
                          ? wordIcon
                          : ''
                      "
                    />
                    <span class="attachment-name">{{ attachment?.name }}</span>
                    <img
                      class="download-icon"
                      src="@/assets/images/detail/download.png"
                      width="15"
                      height="14"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 审核信息 -->
          <div class="info-section mb-[20px]">
            <div class="section-title">
              <img
                src="@/assets/images/detail/review-info.png"
                alt=""
                width="14"
                height="14"
              />
              <span class="title-text">审核信息</span>
            </div>
            <div class="review-content">
              <div class="review-status mb-[5px]">
                <span
                  class="status-label"
                  :class="
                    item?.approveStatus === '通过'
                      ? 'status-passed'
                      : 'status-failed'
                  "
                  >{{ item?.approveStatus }}</span
                >
                <span class="review-time">{{ item?.approveAt }}</span>
              </div>

              <div class="review-description" v-if="item?.approveOpinion">
                <div class="description-text">{{ item?.approveOpinion }}</div>
              </div>
            </div>
          </div>

          <div class="info-section mb-[20px]">
            <div class="section-title">
              <img
                src="@/assets/images/detail/review-answer.png"
                alt=""
                width="14"
                height="14"
              />
              <span class="title-text">回复信息</span>
            </div>
            <div class="reply-content">
              <!-- 两行两列布局：办理单位、回复时间、经办人姓名、经办人电话 -->
              <div class="info-grid grid-2 mb-[20px]">
                <div class="info-item no-padding-bg">
                  <span class="label">办理单位</span>
                  <span class="value">{{ item?.replyOrganization }}</span>
                </div>
                <div class="info-item no-padding-bg">
                  <span class="label">回复时间</span>
                  <span class="value">{{ item?.replyTime }}</span>
                </div>
              </div>

              <!-- 回复内容单独一行 -->
              <div class="reply-description mb-[20px]">
                <div class="label mb-[8px]">回复内容</div>
                <div class="description-text no-padding-bg">
                  {{ item?.replyContent }}
                </div>
              </div>

              <!-- 回复附件单独一行 -->
              <div
                class="attachments"
                v-if="item?.replyFiles && item?.replyFiles.length > 0"
              >
                <div class="label mb-[12px]">回复附件</div>
                <div class="attachment-list">
                  <div
                    v-for="(attachment, attachIndex) in item?.replyFiles"
                    :key="attachIndex"
                    class="attachment-item"
                    @click="downloadAttachment(attachment)"
                  >
                    <img
                      class="attachment-icon"
                      :src="
                        attachment.type === 'img'
                          ? imageIcon
                          : attachment.type === 'pdf'
                          ? pdfIcon
                          : attachment.type === 'word' ||
                            attachment.type === 'doc'
                          ? wordIcon
                          : ''
                      "
                    />
                    <span class="attachment-name">{{ attachment.name }}</span>
                    <img
                      class="download-icon"
                      src="@/assets/images/detail/download.png"
                      width="15"
                      height="14"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 省厅督办意见 -->
          <!-- <div class="info-section mb-[20px]" v-if="activeTab === '1'">
            <div class="section-title">
              <img
                src="@/assets/images/detail/supervise-advice.png"
                alt=""
                width="14"
                height="14"
              />
              <span class="title-text">省厅督办意见</span>
            </div>
            <div class="supervision-content">
              <div class="supervision-header mb-[20px]">
                <div class="supervision-opinion">
                  <div class="label mb-[8px]">督办意见</div>
                  <div class="description-text">{{ item?.opinion }}</div>
                </div>
                <div class="supervision-time">
                  <div class="label mb-[8px]">回复时间</div>
                  <div class="time-text">{{ item?.replyTime }}</div>
                </div>
              </div>

              <div
                class="attachments"
                v-if="
                  complaintData?.supervisionOpinion?.attachments &&
                  complaintData?.supervisionOpinion?.attachments.length > 0
                "
              >
                <div class="label mb-[12px]">督办附件</div>
                <div class="attachment-list">
                  <div
                    v-for="(attachment, attachIndex) in complaintData
                      .supervisionOpinion?.attachments"
                    :key="attachIndex"
                    class="attachment-item"
                    @click="downloadAttachment(attachment)"
                  >
                    <img
                      class="attachment-icon"
                      :src="
                        attachment.type === 'img'
                          ? imageIcon
                          : attachment.type === 'pdf'
                          ? pdfIcon
                          : attachment.type === 'word' ||
                            attachment.type === 'doc'
                          ? wordIcon
                          : ''
                      "
                    />
                    <span class="attachment-name">{{ attachment.name }}</span>
                    <img
                      class="download-icon"
                      src="@/assets/images/detail/download.png"
                      width="15"
                      height="14"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div> -->
        </template>
      </template>

      <template v-if="activeTab === '3'">
        <template
          v-for="(item, index) in complaintData?.complaintSuperviseLogs"
          :key="index"
        >
          <div class="info-section mb-[20px]">
            <div class="section-title">
              <img
                src="@/assets/images/detail/supervise-info.png"
                alt=""
                width="14"
                height="14"
              />
              <span class="title-text">{{
                index === 0 ? "最新督办意见" : "督办意见"
              }}</span>
            </div>
            <div class="reply-content">
              <div class="reply-description mb-[20px]">
                <div class="label mb-[8px]">
                  <span>督办意见</span>
                  <span class="ml-[16px]">{{ item?.superviseAt }}</span>
                </div>
                <div class="description-text no-padding-bg">
                  {{ item?.superviseOpinion }}
                </div>
              </div>
              <div
                class="attachments"
                v-if="item?.superviseFiles && item?.superviseFiles.length > 0"
              >
                <div class="label mb-[12px]">回复附件</div>
                <div class="attachment-list">
                  <div
                    v-for="(attachment, attachIndex) in item?.superviseFiles"
                    :key="attachIndex"
                    class="attachment-item"
                    @click="downloadAttachment(attachment)"
                  >
                    <img
                      class="attachment-icon"
                      :src="
                        attachment?.type === 'image'
                          ? imageIcon
                          : attachment?.type === 'pdf'
                          ? pdfIcon
                          : attachment?.type === 'word'
                          ? wordIcon
                          : ''
                      "
                    />
                    <span class="attachment-name">{{ attachment?.name }}</span>
                    <img
                      class="download-icon"
                      src="@/assets/images/detail/download.png"
                      width="15"
                      height="14"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 最新督办回复 -->
          <div class="info-section mb-[20px]" v-if="item.superviseOpinion">
            <div class="section-title">
              <img
                src="@/assets/images/detail/review-answer.png"
                alt=""
                width="14"
                height="14"
              />
              <span class="title-text">{{
                index === 0 ? "最新督办回复" : "督办回复"
              }}</span>
            </div>
            <div class="reply-content">
              <!-- 两行两列布局：办理单位、回复时间、经办人姓名、经办人电话 -->
              <div class="info-grid grid-2 mb-[20px]">
                <div class="info-item no-padding-bg">
                  <span class="label">办理单位</span>
                  <span class="value">{{ item.replyOrganization }}</span>
                </div>
                <div class="info-item no-padding-bg">
                  <span class="label">回复时间</span>
                  <span class="value">{{ item?.replyAt }}</span>
                </div>
                <div class="info-item no-padding-bg">
                  <span class="label">经办人姓名</span>
                  <span class="value">{{ item?.replyTitle }}</span>
                </div>
                <div class="info-item no-padding-bg">
                  <span class="label">经办人电话</span>
                  <span class="value">{{ item?.replyMobile }}</span>
                </div>
              </div>

              <!-- 回复内容单独一行 -->
              <div class="reply-description mb-[20px]">
                <div class="label mb-[8px]">回复内容</div>
                <div class="description-text no-padding-bg">
                  {{ item?.replyContent }}
                </div>
              </div>

              <!-- 回复附件单独一行 -->
              <div
                class="attachments"
                v-if="item?.replyFiles && item?.replyFiles.length > 0"
              >
                <div class="label mb-[12px]">回复附件</div>
                <div class="attachment-list">
                  <div
                    v-for="(attachment, attachIndex) in item?.replyFiles"
                    :key="attachIndex"
                    class="attachment-item"
                    @click="downloadAttachment(attachment)"
                  >
                    <img
                      class="attachment-icon"
                      :src="
                        attachment?.type === 'image'
                          ? imageIcon
                          : attachment?.type === 'pdf'
                          ? pdfIcon
                          : attachment?.type === 'word'
                          ? wordIcon
                          : ''
                      "
                    />
                    <span class="attachment-name">{{ attachment?.name }}</span>
                    <img
                      class="download-icon"
                      src="@/assets/images/detail/download.png"
                      width="15"
                      height="14"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </template>
      </template>

      <!-- 分办表单 -->
      <div
        class="info-section mb-[20px]"
        v-if="type === 'distribution' && activeTab === '1'"
      >
        <div class="section-title">
          <img
            src="@/assets/images/detail/supervise-info.png"
            alt=""
            width="14"
            height="14"
          />
          <span class="title-text">分办信息</span>
        </div>
        <div class="assignment-form-content">
          <el-form
            ref="assignmentFormRef"
            :model="assignmentForm"
            :rules="assignmentRules"
            label-position="top"
          >
            <!-- 分办单位 -->
            <el-form-item
              label="分办单位"
              prop="assignmentUnit"
              class="mb-[20px]"
            >
              <el-select
                v-model="assignmentForm.assignmentUnit"
                placeholder="请选择分办单位"
                class="assignment-unit-select"
              >
                <el-option
                  v-for="option in assignmentUnitOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>

            <!-- 分办意见 -->
            <el-form-item prop="assignmentOpinion" class="mb-[20px]">
              <template #label>
                <span class="assignment-opinion-label">
                  分办意见
                  <span class="assignment-opinion-hint">（不超过100字）</span>
                </span>
              </template>
              <el-input
                v-model="assignmentForm.assignmentOpinion"
                type="textarea"
                :rows="4"
                placeholder="请输入分办意见..."
                maxlength="100"
                show-word-limit
                class="assignment-opinion-input"
              />
            </el-form-item>
          </el-form>
        </div>
      </div>

      <!-- 审核表单 -->
      <div
        class="info-section mb-[20px]"
        v-if="
          activeTab === '1' &&
          type === 'review' &&
          complaintData.approveStatus != 1
        "
      >
        <div class="section-title">
          <img
            src="@/assets/images/detail/review-info.png"
            alt=""
            width="14"
            height="14"
          />
          <span class="title-text">审核信息</span>
        </div>
        <div class="review-form-content">
          <el-form
            ref="reviewFormRef"
            :model="reviewForm"
            :rules="reviewRules"
            label-position="top"
          >
            <!-- 审核状态 -->
            <el-form-item label="审核状态" prop="status" class="mb-[20px]">
              <el-select
                v-model="reviewForm.status"
                placeholder="请选择审核状态"
                @change="handleReviewStatusChange"
                class="review-status-select"
              >
                <el-option
                  v-for="option in reviewStatusOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>

            <!-- 不通过原因 -->
            <el-form-item
              v-if="reviewForm.status === '2'"
              prop="reason"
              class="mb-[20px]"
            >
              <template #label>
                <span class="reason-label">
                  不通过原因
                  <span class="reason-hint">（不超过50字）</span>
                </span>
              </template>
              <el-input
                v-model="reviewForm.reason"
                placeholder="请输入不通过原因..."
                maxlength="50"
                show-word-limit
                class="reason-input"
              />
            </el-form-item>
          </el-form>
        </div>
      </div>

      <!-- 回复信息表单 -->
      <div
        class="info-section mb-[20px]"
        v-if="
          activeTab === '1' && (type === 'reply' || type === 'add-supervise')
        "
      >
        <div class="section-title">
          <img
            v-if="type === 'add-supervise'"
            src="@/assets/images/detail/supervise-advice.png"
            alt=""
            width="14"
            height="14"
          />
          <img
            v-else
            src="@/assets/images/detail/answer-info.png"
            alt=""
            width="14"
            height="11"
          />
          <span class="title-text">{{
            type === "add-supervise" ? "省厅督办意见" : "回复信息"
          }}</span>
        </div>
        <div class="reply-form-content">
          <el-form
            ref="replyFormRef"
            :model="replyForm"
            :rules="replyRules"
            label-position="top"
          >
            <!-- 办理单位 (仅督办意见显示) -->
            <el-form-item
              v-if="type === 'add-supervise' && !complaintData.superviseOpinion"
              label="办理单位"
              prop="superviseBy"
              class="mb-[20px]"
            >
              <el-radio-group
                v-model="replyForm.superviseBy"
                class="handle-unit-radio"
              >
                <el-radio
                  v-for="option in superviseByOptions"
                  :key="option.value"
                  :value="option.value"
                >
                  {{ option.label }}
                </el-radio>
              </el-radio-group>
            </el-form-item>

            <!-- 回复内容 -->
            <el-form-item
              :label="type === 'add-supervise' ? '督办意见' : '回复信息'"
              prop="content"
              class="mb-[20px]"
            >
              <el-input
                v-model="replyForm.content"
                type="textarea"
                :rows="6"
                :placeholder="`请输入详细的${
                  type === 'add-supervise' ? '督办意见' : '回复信息'
                }...`"
                maxlength="1000"
                show-word-limit
                class="reply-textarea"
              />
            </el-form-item>

            <!-- 回复附件 -->
            <el-form-item class="mb-[20px]">
              <template #label>
                <span class="attachment-label">
                  {{ type === "add-supervise" ? "督办附件" : "回复附件" }}
                  <span class="attachment-hint"
                    >（不超过5个，支持jpg/doc/pdf/png）</span
                  >
                </span>
              </template>
              <div class="upload-area">
                <!-- 隐藏的文件输入 -->
                <input
                  ref="fileInputRef"
                  type="file"
                  multiple
                  accept=".jpg,.jpeg,.png,.doc,.docx,.pdf"
                  style="display: none"
                  @change="handleFileInputChange"
                />

                <!-- 自定义上传区域 -->
                <div
                  class="custom-upload-dragger"
                  :class="{ 'drag-over': isDragOver }"
                  @click="handleUploadClick"
                  @dragover="handleDragOver"
                  @dragleave="handleDragLeave"
                  @drop="handleDrop"
                >
                  <div class="upload-content">
                    <el-icon color="#1A68A8" size="48">
                      <UploadFilled />
                    </el-icon>
                    <div class="upload-text">点击或拖拽文件到此处上传</div>
                    <div class="upload-hint">
                      支持 jpg、doc、pdf、png格式，最多上传5个文件
                    </div>
                  </div>
                </div>

                <!-- 文件列表 -->
                <div class="file-list" v-if="fileList.length > 0">
                  <div
                    v-for="file in fileList"
                    :key="`${file.id}-${forceUpdate}`"
                    class="file-item"
                  >
                    <img
                      class="file-icon"
                      :src="
                        file.type === 'image'
                          ? imageIcon
                          : file.type === 'pdf'
                          ? pdfIcon
                          : wordIcon
                      "
                    />
                    <span class="file-name">{{ file.name }}</span>
                    <span class="file-size"
                      >({{ formatFileSize(file.size) }})</span
                    >
                    <!-- 添加调试信息 -->
                    <!-- <span class="debug-info" style="font-size: 10px; color: #999;">
                      Debug: uploading={{ file.uploading }}, cosUrl={{ !!file.cosUrl }}, forceUpdate={{ forceUpdate }}
                    </span> -->
                    <span v-if="file.uploading" class="upload-status"
                      >上传中...</span
                    >
                    <span v-else-if="file.cosUrl" class="upload-status success"
                      >上传成功</span
                    >
                    <el-icon class="remove-icon" @click="removeFile(file.id)">
                      <Close />
                    </el-icon>
                  </div>
                </div>
              </div>
            </el-form-item>

            <!-- 经办人信息 -->
            <div class="info-grid grid-2" v-if="type !== 'add-supervise'">
              <el-form-item label="经办人姓名" prop="replyTitle">
                <el-input
                  v-model="replyForm.replyTitle"
                  placeholder="请输入经办人姓名"
                  class="handler-input"
                />
              </el-form-item>
              <el-form-item label="经办人电话" prop="replyMobile">
                <el-input
                  v-model="replyForm.replyMobile"
                  placeholder="请输入经办人电话"
                  class="handler-input"
                />
              </el-form-item>
            </div>
          </el-form>
        </div>
      </div>
    </section>
    <footer class="flex-end-center gap-[8px] mt-[24px]">
      <el-button class="common-button-6" @click="router.back()">
        <template #icon>
          <img
            src="@/assets/images/detail/back.png"
            alt=""
            width="14"
            height="12"
          />
        </template>
        返回
      </el-button>
      <template v-if="type !== 'detail' && activeTab === '1'">
        <el-button class="common-button-6" @click="handleSave">
          <template #icon>
            <img
              src="@/assets/images/detail/save.png"
              alt=""
              width="14"
              height="14"
            />
          </template>
          保存
        </el-button>
        <el-button class="common-button-7" @click="handleSubmit">
          <template #icon>
            <img
              src="@/assets/images/detail/submit.png"
              alt=""
              width="15"
              height="14"
            />
          </template>
          提交
        </el-button>
      </template>
    </footer>

    <!-- 查看延期弹窗 -->
    <el-dialog
      v-model="extensionDialogVisible"
      title="查看延期"
      width="480px"
      class="custom-dialog"
      :before-close="handleCloseExtensionDialog"
    >
      <el-form
        ref="extensionFormRef"
        :model="extensionForm"
        label-position="top"
        class="extension-form"
      >
        <el-form-item label="延期时间">
          <div class="flex items-center gap-2">
            <el-input
              v-model="extensionForm.days"
              placeholder="请输入天数"
              style="width: 80px"
              disabled
            />
            <span class="text-gray-600">天</span>
          </div>
        </el-form-item>

        <el-form-item>
          <template #label>
            <span class="attachment-label">附件</span>
          </template>
          <div class="upload-area">
            <!-- 文件列表 -->
            <div class="file-list" v-if="extensionFileList.length > 0">
              <div
                v-for="file in extensionFileList"
                :key="file.id"
                class="file-item"
              >
                <img
                  class="file-icon"
                  :src="
                    file.type === 'image'
                      ? imageIcon
                      : file.type === 'pdf'
                      ? pdfIcon
                      : wordIcon
                  "
                />
                <span class="file-name">{{ file.name }}</span>

                <!-- 查看模式显示下载按钮 -->
                <el-icon
                  class="download-icon"
                  @click="downloadExtensionAttachment(file)"
                >
                  <Download />
                </el-icon>
              </div>
            </div>

            <!-- 没有文件时的提示 -->
            <div v-if="extensionFileList.length === 0" class="no-files-tip">
              暂无附件
            </div>
          </div>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="handleCloseExtensionDialog"
            >返回</el-button
          >
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
.detail {
  .content {
    .content-header {
      .header-title {
        font-size: 18px;
        font-weight: 600;
        color: #2b2c33;
        line-height: 26px;
      }

      .header-right {
        .time-limit-display {
          .time-limit-tag {
            font-size: 14px;
            font-weight: 500;
          }
        }
      }
    }

    .info-section {
      .section-title {
        display: flex;
        align-items: center;
        margin-bottom: 14px;

        .title-icon {
          width: 4px;
          height: 20px;
          background: linear-gradient(180deg, #0ec3ed 0%, #239dde 100%);
          border-radius: 2px;
          margin-right: 12px;
        }

        .title-text {
          font-size: 14px;
          line-height: 20px;
          font-weight: 600;
          color: #2b2c33;
          padding-left: 6px;
        }
      }

      .info-grid {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 12px;

        &.grid-3 {
          grid-template-columns: repeat(3, 1fr);
          gap: 18px;
        }

        &.grid-2 {
          grid-template-columns: repeat(2, 1fr);
          gap: 12px;
        }

        .info-item {
          display: flex;
          flex-direction: column;
          align-items: flex-start;
          padding: 10px 16px;
          background: #fafafa;
          border-radius: 8px 8px 8px 8px;
          border: 1px solid #e2e3e6;

          &.no-padding-bg {
            padding: 0;
            background: transparent;
            border: none;
            border-radius: 0;
          }

          .label {
            font-size: 14px;
            line-height: 20px;
            font-weight: 400;
            color: #94959c;
            margin-bottom: 4px;
          }

          .value {
            font-size: 16px;
            line-height: 24px;
            color: #2b2c33;
          }
        }
      }

      .problem-content {
        .problem-description {
          .label {
            font-size: 14px;
            font-weight: 400;
            color: #94959c;
          }

          .description-text {
            font-size: 16px;
            color: #2b2c33;
            line-height: 24px;
            padding: 16px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #e2e3e6;

            &.no-padding-bg {
              padding: 0;
              background: transparent;
              border: none;
              border-radius: 0;
            }
          }
        }

        .attachments {
          padding: 12px 16px;
          background: #f8f9fa;
          border: 1px solid #e2e3e6;
          border-radius: 8px;

          .label {
            font-size: 14px;
            font-weight: 400;
            color: #94959c;
          }

          .attachment-list {
            display: flex;
            justify-content: flex-start;
            align-items: center;
            flex-wrap: wrap;
            gap: 12px;

            .attachment-item {
              display: flex;
              justify-content: flex-start;
              align-items: center;
              cursor: pointer;
              transition: all 0.3s ease;
              background: #ffffff;
              border-radius: 8px 8px 8px 8px;
              border: 1px solid #e2e3e6;
              padding: 8px 13px;

              .attachment-icon {
                width: 15px;
                height: 16px;
                margin-right: 11px;
              }

              .attachment-name {
                font-size: 16px;
                color: #2b2c33;
                flex: 1;
              }

              .download-icon {
                margin-left: 11px;
              }
            }
          }
        }
      }

      .assignment-content {
        .assignment-description {
          padding: 16px;
          background: #f8f9fa;
          border-radius: 8px;
          border: 1px solid #e2e3e6;

          &.no-padding-bg {
            padding: 0;
            background: transparent;
            border: none;
            border-radius: 0;
          }

          .label {
            font-size: 14px;
            color: #6d6f75;
          }

          .description-text {
            font-size: 16px;
            line-height: 24px;
            color: #2b2c33;
          }
        }
      }

      // 回复信息和审核回复样式
      .reply-content {
        padding: 16px;
        background: #f8f9fa;
        border-radius: 8px;
        border: 1px solid #e2e3e6;

        .reply-description {
          .label {
            font-size: 14px;
            color: #6d6f75;
          }

          .description-text {
            font-size: 16px;
            color: #2b2c33;
            line-height: 24px;
            padding: 16px;
            background: #ffffff;
            border-radius: 8px;
            border: 1px solid #e2e3e6;

            &.no-padding-bg {
              padding: 0;
              background: transparent;
              border: none;
              border-radius: 0;
            }
          }
        }

        .attachments {
          .label {
            font-size: 14px;
            color: #6d6f75;
          }

          .attachment-list {
            display: flex;
            justify-content: flex-start;
            align-items: center;
            flex-wrap: wrap;
            gap: 12px;

            .attachment-item {
              display: flex;
              justify-content: flex-start;
              align-items: center;
              cursor: pointer;
              transition: all 0.3s ease;
              background: #ffffff;
              border-radius: 8px 8px 8px 8px;
              border: 1px solid #e2e3e6;
              padding: 8px 13px;

              .attachment-icon {
                width: 15px;
                height: 16px;
                margin-right: 11px;
              }

              .attachment-name {
                font-size: 16px;
                color: #2b2c33;
                flex: 1;
              }

              .download-icon {
                margin-left: 11px;
              }
            }
          }
        }
      }

      // 审核信息样式
      .review-content {
        padding: 16px;
        background: #f8f9fa;
        border-radius: 8px;
        border: 1px solid #e2e3e6;

        .review-status {
          display: flex;
          align-items: center;
          gap: 16px;

          .status-label {
            font-size: 12px;
            height: 20px;
            line-height: 20px;
            padding: 4px 8px;
            border-radius: 10px;
            display: inline-flex;
            align-items: center;

            &.status-passed {
              color: #28b28b;
              background-color: #dcfce7;
            }

            &.status-failed {
              color: #e72c4a;
              background-color: #ffe9e9;
            }
          }

          .review-time {
            font-size: 14px;
            color: #6d6f75;
          }
        }

        .review-description {
          .description-text {
            font-size: 16px;
            color: #2b2c33;
            line-height: 24px;
          }
        }
      }

      // 分办表单样式
      .assignment-form-content {
        :deep(.el-form) {
          .el-form-item {
            .el-select {
              width: 100%;

              &__wrapper {
                width: 100%;
                height: 40px;
              }
            }

            .assignment-opinion-input {
              .el-textarea__inner {
                border-radius: 8px;
                font-size: 16px;
                color: #2b2c33;
                line-height: 24px;
                resize: none;

                &:focus {
                  border-color: #0ec3ed;
                  box-shadow: 0 0 0 2px rgba(14, 195, 237, 0.1);
                }

                &::placeholder {
                  color: #94959c;
                }
              }
            }
          }
        }
      }

      // 审核表单样式
      .review-form-content {
        :deep(.el-form) {
          .el-form-item {
            .el-select {
              width: 100%;

              &__wrapper {
                width: 100%;
                height: 40px;
              }
            }

            .el-input {
              &__wrapper {
                height: 40px;
              }
            }
          }
        }
      }

      // 省厅督办意见样式
      .supervision-content {
        padding: 16px;
        background: #f8f9fa;
        border-radius: 8px;
        border: 1px solid #e2e3e6;

        .supervision-header {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 24px;

          .supervision-opinion {
            flex: 1;

            .label {
              font-size: 14px;
              color: #6d6f75;
            }

            .description-text {
              font-size: 16px;
              color: #2b2c33;
              line-height: 24px;
            }
          }

          .supervision-time {
            .label {
              font-size: 14px;
              color: #6d6f75;
            }

            .time-text {
              font-size: 16px;
              color: #2b2c33;
              line-height: 24px;
            }
          }
        }

        .attachments {
          .label {
            font-size: 14px;
            color: #6d6f75;
          }

          .attachment-list {
            display: flex;
            justify-content: flex-start;
            align-items: center;
            flex-wrap: wrap;
            gap: 12px;

            .attachment-item {
              display: flex;
              justify-content: flex-start;
              align-items: center;
              cursor: pointer;
              transition: all 0.3s ease;
              background: #ffffff;
              border-radius: 8px 8px 8px 8px;
              border: 1px solid #e2e3e6;
              padding: 8px 13px;

              .attachment-icon {
                width: 15px;
                height: 16px;
                margin-right: 11px;
              }

              .attachment-name {
                font-size: 16px;
                color: #2b2c33;
                flex: 1;
              }

              .download-icon {
                margin-left: 11px;
              }
            }
          }
        }
      }

      // 回复表单样式
      .reply-form-content {
        .handle-unit-radio {
          display: flex;
          gap: 24px;

          :deep(.el-radio) {
            margin-right: 0;

            .el-radio__label {
              font-size: 16px;
              color: #2b2c33;
            }

            .el-radio__input.is-checked .el-radio__inner {
              background-color: #fff;
              border-color: rgba($color: #000000, $alpha: 0.15);

              &::after {
                background-color: #1a68a8;
                width: 8px;
                height: 8px;
              }
            }
          }
        }
      }
    }
  }
}

.tab-container {
  :deep(.el-tabs) {
    .el-tabs__nav-wrap::after {
      display: none;
    }

    .el-tabs__item {
      color: #9e9e9e;
      font-size: 20px;

      &.is-active {
        color: #1a68a8 !important;
        font-weight: 600;
      }

      &:hover {
        color: #1a68a8 !important;
      }
    }

    .el-tabs__active-bar {
      background-color: #1a68a8;
    }
  }
}

:deep(.el-form) {
  .el-form-item {
    margin-bottom: 0;

    .el-form-item__label {
      font-size: 14px;
      font-weight: 600;
      color: #2b2c33;
      margin-bottom: 8px;

      .attachment-label {
        .attachment-hint {
          font-weight: 400;
          color: #94959c;
        }
      }

      .assignment-opinion-label {
        .assignment-opinion-hint {
          font-weight: 400;
          color: #94959c;
        }
      }
    }

    .reply-textarea {
      .el-textarea__inner {
        border-radius: 8px;
        font-size: 16px;
        color: #2b2c33;
        line-height: 24px;
        resize: none;

        &:focus {
          border-color: #0ec3ed;
          box-shadow: 0 0 0 2px rgba(14, 195, 237, 0.1);
        }

        &::placeholder {
          color: #94959c;
        }
      }
    }

    .handler-input {
      .el-input__inner {
        border-radius: 8px;
        font-size: 16px;
        color: #2b2c33;
        height: 40px;

        &:focus {
          border-color: #0ec3ed;
          box-shadow: 0 0 0 2px rgba(14, 195, 237, 0.1);
        }

        &::placeholder {
          color: #94959c;
        }
      }
    }
  }

  .upload-area {
    width: 100%;

    .custom-upload-dragger {
      background: #ffffff;
      border: 1px dashed #e2e3e6;
      border-radius: 8px;
      width: 100%;
      height: 126px;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s ease;
      cursor: pointer;

      &:hover,
      &.drag-over {
        border-color: #0ec3ed;
        background: #f8fcff;
      }

      .upload-content {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        height: 100%;
        text-align: center;
        pointer-events: none;

        .el-icon {
          width: 33px;
          height: 24px;
        }

        .upload-text {
          font-size: 16px;
          color: #2b2c33;
          line-height: 24px;
          margin-top: 6px;
        }

        .upload-hint {
          font-size: 14px;
          color: #94959c;
          line-height: 20px;
        }
      }
    }

    .file-list {
      margin-top: 16px;

      .file-item {
        display: flex;
        align-items: center;
        padding: 8px 12px;
        background: #ffffff;
        border: 1px solid #e2e3e6;
        border-radius: 6px;
        margin-bottom: 8px;
        transition: all 0.3s ease;

        &:hover {
          background: #f8fcff;
          border-color: #0ec3ed;
        }

        .file-icon {
          width: 20px;
          height: 20px;
          margin-right: 12px;
          flex-shrink: 0;
        }

        .file-name {
          font-size: 14px;
          color: #2b2c33;
          flex: 1;
          margin-right: 8px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .file-size {
          font-size: 12px;
          color: #94959c;
          margin-right: 12px;
          flex-shrink: 0;
        }

        .upload-status {
          font-size: 12px;
          color: #94959c;
          margin-right: 12px;
          flex-shrink: 0;

          &.success {
            color: #28b28b;
          }
        }

        .remove-icon {
          font-size: 16px;
          color: #94959c;
          cursor: pointer;
          flex-shrink: 0;
          transition: color 0.3s ease;

          &:hover {
            color: #e72c4a;
          }
        }
      }
    }
  }
}

// 延期弹窗样式
.extension-form {
  .attachment-label {
    font-size: 16px;
    font-weight: 600;
    color: var(--grey1);
  }

  .upload-area {
    width: 100%;

    .file-list {
      .file-item {
        display: flex;
        align-items: center;
        padding: 8px 12px;
        background: #f8f9fa;
        border-radius: 6px;
        margin-bottom: 8px;

        .file-icon {
          width: 15px;
          height: 16px;
          color: #1a68a8;
          margin-right: 8px;
        }

        .file-name {
          flex: 1;
          font-size: 14px;
          color: #2b2c33;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .download-icon {
          font-size: 16px;
          color: #1a68a8;
          cursor: pointer;
          margin-left: 8px;

          &:hover {
            color: #0ec3ed;
          }
        }
      }
    }

    .no-files-tip {
      padding: 20px;
      text-align: center;
      color: #94959c;
      font-size: 14px;
      background: #f8f9fa;
      border-radius: 6px;
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
