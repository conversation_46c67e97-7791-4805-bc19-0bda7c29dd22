<script setup>
import TopTitle from "@/views/components/TopTitle/index.vue";
import { Search, RefreshRight } from "@element-plus/icons-vue";
import { getComplaintListApi, getDelayDetailApi } from '@/apis/complaint';
import { getComplaintTypeList, getSchoolStageList, getStatusListApi } from "@/apis/common";
import { useUserStore } from "@/store/modules/user";
import dayjs from 'dayjs';
import { Download } from "@element-plus/icons-vue";

import imageIcon from "@/assets/images/detail/image.png";
import wordIcon from "@/assets/images/detail/word.png";
import pdfIcon from "@/assets/images/detail/pdf.png";

const router = useRouter()
const userStore = useUserStore()

const searchForm = ref({
  startTime: "", // 开始时间
  endTime: "", // 结束时间
  complaintType: "", // 问题分类
  stageId: "", // 投诉对象类型（学校类型）
  status: "", // 处置状态
  userMobile: "", // 投诉人手机号
  content: "", // 内容
  isDelay: "", // 是否超时
  institutionLevel: "", // 处置机构级别
});

// 问题分类选项
const complaintTypeOptions = ref([
  { label: "全部", value: "" }
]);

// 学校类型选项（投诉对象类型）
const schoolStageOptions = ref([
  { label: "全部", value: "" }
]);

// 状态选项
const statusOptions = ref([
  { label: "全部", value: "" }
]);

// 是否超时选项
const isOvertimeOptions = [
  { label: "全部", value: "" },
  { label: "是", value: "1" },
  { label: "否", value: "0" },
];

// 处置机构级别选项 - 根据当前用户级别动态计算
const institutionLevelOptions = computed(() => {
  const currentUserRole = userStore.userInfo?.role;

  if (currentUserRole === 3) {
    // 省级用户：全部、省级、市级、县区
    return [
      { label: "全部", value: "" },
      { label: "省级", value: "3" },
      { label: "市级", value: "2" },
      { label: "县区", value: "1" },
    ];
  } else if (currentUserRole === 2) {
    // 市级用户：全部、市级、县区
    return [
      { label: "全部", value: "" },
      { label: "市级", value: "2" },
      { label: "县区", value: "1" },
    ];
  } else {
    // 县区用户：不显示该筛选
    return [];
  }
});

// 加载投诉问题分类
const loadComplaintTypeList = async () => {
  try {
    const { data } = await getComplaintTypeList();
    complaintTypeOptions.value = [
      { label: "全部", value: "" },
      ...(data || []).map(item => ({
        label: item.title,
        value: item.id
      }))
    ];
  } catch (error) {
    console.error("加载投诉问题分类失败:", error);
  }
};

// 加载学校类型
const loadSchoolStageList = async () => {
  try {
    const { data } = await getSchoolStageList();
    schoolStageOptions.value = [
      { label: "全部", value: "" },
      ...(data || []).map(item => ({
        label: item.title,
        value: item.id
      }))
    ];
  } catch (error) {
    console.error("加载学校类型失败:", error);
  }
};

// 加载状态列表 - 过滤出值为2/5/7三项
const loadStatusList = async () => {
  try {
    const { data } = await getStatusListApi();
    const allStatusOptions = [
      { label: "全部", value: "" },
      ...(data || []).map(item => ({
        label: item.title,
        value: item.id
      }))
    ];
    // 过滤出值为2/5/7的状态选项
    statusOptions.value = allStatusOptions.filter(item =>
      item.value === "" || item.value === "2" || item.value === "5" || item.value === "7"
    );
  } catch (error) {
    console.error("加载状态列表失败:", error);
  }
};

// 日期验证：结束时间不能早于开始时间
const validateDateRange = () => {
  if (searchForm.value.startTime && searchForm.value.endTime) {
    if (
      new Date(searchForm.value.endTime) < new Date(searchForm.value.startTime)
    ) {
      ElMessage.warning("结束时间不能早于开始时间");
      searchForm.value.endTime = "";
    }
  }
};

// 开始时间变化时的处理
const handleStartDateChange = () => {
  validateDateRange();
};

// 结束时间变化时的处理
const handleEndDateChange = () => {
  validateDateRange();
};

// 搜索功能
const handleSearch = async () => {
  // 验证日期范围
  if (searchForm.value.startTime && searchForm.value.endTime) {
    if (
      new Date(searchForm.value.endTime) < new Date(searchForm.value.startTime)
    ) {
      ElMessage.warning("结束时间不能早于开始时间");
      return;
    }
  }

  await loadComplaintList();
};

// 重置功能
const handleReset = () => {
  searchForm.value = {
    startTime: "",
    endTime: "",
    complaintType: "",
    stageId: "",
    status: "",
    userMobile: "",
    content: "",
    isDelay: "",
    institutionLevel: "",
  };
  console.log("表单已重置");
};

// 表格数据
const tableData = ref([]);

// 加载投诉列表
const loadComplaintList = async () => {
  try {
    const params = {
      ...searchForm.value,
      completed: 1, // 固定传1（指历史记录）
      pageNum: pagination.value.currentPage,
      pageSize: pagination.value.pageSize
    };

    // 移除空值参数
    Object.keys(params).forEach(key => {
      if (params[key] === "" || params[key] === null || params[key] === undefined) {
        delete params[key];
      }
    });

    const { data } = await getComplaintListApi(params);
    tableData.value = data.list || [];
    pagination.value.total = data.total || 0;
  } catch (error) {
    console.error("加载历史处置记录列表失败:", error);
  }
};

// 分页数据
const pagination = ref({
  currentPage: 1,
  pageSize: 10,
  total: 0,
});

// 分页变化处理
const handleCurrentChange = (page) => {
  pagination.value.currentPage = page;
  loadComplaintList();
};

// 刷新数据
const handleRefresh = () => {
  loadComplaintList();
};

// 导出数据
const handleExport = () => {
  console.log("导出数据");
  ElMessage.success("数据导出成功");
};

// 操作处理
const handleOperation = (row, action) => {
  if (action === 'detail') {
    router.push({
      path: '/historyRecord/detail',
      query: {
        complaintId: row.complaintId,
        type: action
      }
    })
  }
};

// 获取状态CSS类名
const getStatusClass = (status) => {
  const classMap = {
    待分办: "status-pending-assignment",
    待处理: "status-pending-processing",
    待审核: "status-pending-review",
    审核未通过: "status-review-failed",
    已办结: "status-completed",
    "待处理（省厅督办件）": "status-pending-provincial",
    "已办结（省厅督办件）": "status-completed-provincial",
  };
  return classMap[status] || "status-default";
};

// 获取问题分类CSS类名
const getProblemTypeClass = (problemType) => {
  const classMap = {
    食品安全: "problem-type-food-safety",
    膳食经费: "problem-type-meal-funding",
    校外培训: "problem-type-external-training",
    教辅教材征订: "problem-type-textbook-ordering",
    校服定制采购: "problem-type-uniform-procurement",
    其他: "problem-type-other",
  };
  return classMap[problemType] || "problem-type-other";
};

// 获取时限显示文本和样式类
const getTimeLimitInfo = (timeLimit, status) => {
  if (status === "已办结") {
    return {
      text: "已完成",
      class: "time-limit-completed",
    };
  }

  const remainingDays = timeLimit;

  if (remainingDays < 0) {
    return {
      text: `超时${Math.abs(remainingDays)}天`,
      class: "time-limit-overdue",
    };
  }

  let className = "";
  if (remainingDays >= 1 && remainingDays < 3) {
    className = "time-limit-urgent";
  } else if (remainingDays >= 3 && remainingDays <= 5) {
    className = "time-limit-warning";
  } else if (remainingDays > 5) {
    className = "time-limit-normal";
  }

  return {
    text: `剩余${remainingDays}天`,
    class: className,
  };
};

// 查看延期弹窗相关
const extensionDialogVisible = ref(false);
const extensionForm = ref({
  days: 0,
  attachments: []
});
const fileList = ref([]);
const currentRow = ref(null);

// 根据文件名获取文件类型
const getFileType = (fileName) => {
  const extension = fileName.split('.').pop().toLowerCase();
  if (['jpg', 'jpeg', 'png', 'gif'].includes(extension)) {
    return 'image';
  } else if (['pdf'].includes(extension)) {
    return 'pdf';
  } else if (['doc', 'docx'].includes(extension)) {
    return 'word';
  }
  return 'word'; // 默认类型
};

// 查看延期信息
const handleViewExtension = async (row) => {
  try {
    currentRow.value = row;
    extensionDialogVisible.value = true;

    // 调用API获取延期详情
    const { data } = await getDelayDetailApi(row.complaintId);

    // 回填延期时间
    extensionForm.value = {
      days: data.delay || 0,
      attachments: []
    };

    // 回填附件数据
    if (data.delayFiles && Array.isArray(data.delayFiles)) {
      fileList.value = data.delayFiles.map((file, index) => ({
        id: index + 1,
        name: file.name,
        type: getFileType(file.name),
        url: file.url,
        cosUrl: file.url,
        uploading: false
      }));
    } else {
      fileList.value = [];
    }
  } catch (error) {
    console.error('获取延期详情失败:', error);
    extensionDialogVisible.value = false;
  }
};

// 关闭延期弹窗
const handleCloseExtensionDialog = () => {
  extensionDialogVisible.value = false;
  currentRow.value = null;
  fileList.value = [];
};

// 下载附件
const downloadAttachment = (file) => {
  console.log("下载附件:", file.name);
  // 创建一个临时的a标签来下载文件
  const link = document.createElement('a');
  link.href = file.url;
  link.download = file.name;
  link.target = '_blank';
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};

// 页面初始化
onMounted(async () => {
  await Promise.all([
    loadComplaintTypeList(),
    loadSchoolStageList(),
    loadStatusList()
  ]);
  await loadComplaintList();
});
</script>

<template>
  <div class="complaint-distribution app-container">
    <TopTitle title="历史处置记录"></TopTitle>
    <section class="search-form px-[24px] py-[16px] rounded-[2px] bg-[#fff]">
      <el-form :model="searchForm" class="search-form-2" label-position="top">
        <!-- 第一行 -->
        <el-form-item label="处理时间段">
          <div class="w-[100%] flex justify-between items-center gap-[8px]">
            <el-date-picker
              v-model="searchForm.startTime"
              type="date"
              placeholder="年/月/日"
              format="YYYY/MM/DD"
              value-format="YYYY-MM-DD"
              @change="handleStartDateChange"
            />
            <span style="color: #606266; line-height: 48px">至</span>
            <el-date-picker
              v-model="searchForm.endTime"
              type="date"
              placeholder="年/月/日"
              format="YYYY/MM/DD"
              value-format="YYYY-MM-DD"
              :disabled-date="
                (time) =>
                  searchForm.startTime && time < new Date(searchForm.startTime)
              "
              @change="handleEndDateChange"
            />
          </div>
        </el-form-item>

        <el-form-item label="问题分类">
          <el-select v-model="searchForm.complaintType" placeholder="全部">
            <el-option
              v-for="item in complaintTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="处置机构级别" v-if="institutionLevelOptions.length > 0">
          <el-select v-model="searchForm.institutionLevel" placeholder="全部">
            <el-option
              v-for="item in institutionLevelOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="处置状态">
          <el-select v-model="searchForm.status" placeholder="全部">
            <el-option
              v-for="item in statusOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <!-- 第二行 -->
        <el-form-item label="投诉人手机号">
          <el-input
            v-model="searchForm.userMobile"
            placeholder="请输入手机号"
          />
        </el-form-item>

        <el-form-item label="投诉对象类型">
          <el-select v-model="searchForm.stageId" placeholder="全部">
            <el-option
              v-for="item in schoolStageOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="内容">
          <el-input
            v-model="searchForm.content"
            placeholder="请输入关键词"
          />
        </el-form-item>

        <el-form-item label="是否超时">
          <el-select v-model="searchForm.isDelay" placeholder="全部">
            <el-option
              v-for="item in isOvertimeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <!-- 按钮组 -->
        <el-form-item class="hidden-label" label="按钮">
          <el-button class="common-button-3" @click="handleSearch">
            <template #icon>
              <el-icon>
                <Search />
              </el-icon>
            </template>
            查询
          </el-button>
          <el-button class="reset-button" @click="handleReset">
            <template #icon>
              <el-icon><RefreshRight /></el-icon>
            </template>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </section>
    <section
      class="table-container mt-[16px] px-[24px] py-[16px] rounded-[2px] bg-[#fff]"
    >
      <!-- 导出按钮 -->
      <div class="mb-[16px] flex justify-between">
        <el-button class="common-button-4" @click="handleRefresh">
          <template #icon>
            <el-icon><RefreshRight /></el-icon>
          </template>
          刷新
        </el-button>
        <el-button class="common-button-3" @click="handleExport">
          <template #icon>
            <img
              src="@/assets/images/common/export.png"
              alt=""
              width="16"
              height="16"
            />
          </template>
          导出数据
        </el-button>
      </div>

      <!-- 表格 -->
      <el-table
        :data="tableData"
        style="width: 100%"
        class="custom-table"
        stripe
      >
        <!-- 固定左侧列 -->
        <el-table-column
          prop="complaintId"
          label="ID"
          width="80"
          fixed="left"
          align="center"
        />
        <el-table-column
          prop="cityTitle"
          label="地市"
          width="100"
          fixed="left"
          align="center"
        >
          <template #default="{ row }">
            {{ row.cityTitle || '-' }}
          </template>
        </el-table-column>
        <el-table-column
          prop="areaTitle"
          label="区县"
          width="100"
          fixed="left"
          align="center"
        >
          <template #default="{ row }">
            {{ row.areaTitle || '-' }}
          </template>
        </el-table-column>
        <el-table-column
          label="举报人信息"
          width="120"
          fixed="left"
          align="center"
        >
          <template #default="{ row }">
            <div class="whitespace-pre-line">{{ `${row.userTitle || '-'}\n${row.userMobile || '-'}\n${row.identity || '-'}` }}</div>
          </template>
        </el-table-column>

        <!-- 其他列 -->
        <el-table-column label="问题分类" width="160" align="center">
          <template #default="{ row }">
            <span
              v-if="row.complaintTypeTitle"
              :class="[
                'problem-type-tag',
                getProblemTypeClass(row.complaintTypeTitle),
              ]"
            >
              {{ row.complaintTypeTitle }}
            </span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="stageTitle"
          label="学校类型"
          width="100"
          align="center"
        >
          <template #default="{ row }">
            {{ row.stageTitle || '-' }}
          </template>
        </el-table-column>
        <el-table-column
          prop="schoolTitle"
          label="举报学校名称"
          width="150"
          align="center"
        >
          <template #default="{ row }">
            {{ row.schoolTitle || '-' }}
          </template>
        </el-table-column>
        <el-table-column label="举报内容" min-width="200" align="center">
          <template #default="{ row }">
            <div class="text-left">{{ row.problemDescription }}</div>
          </template>
        </el-table-column>
        <el-table-column
          prop="createAt"
          label="留言时间"
          width="120"
          align="center"
        >
          <template #default="{ row }">
            <div>{{ row.createAt && dayjs(row.createAt).format("YYYY-MM-DD") || '-' }}</div>
          </template>
        </el-table-column>
        <el-table-column
          prop="replyOrganization"
          label="办理单位"
          width="120"
          align="center"
        >
          <template #default="{ row }">
            <div>{{ row.replyOrganization || '-' }}</div>
          </template>
        </el-table-column>
        <el-table-column
          prop="deadAt"
          label="分办时间"
          width="120"
          align="center"
        >
          <template #default="{ row }">
            <div>{{ row.deadAt && dayjs(row.deadAt).format("YYYY-MM-DD") || '-' }}</div>
          </template>
        </el-table-column>
        <el-table-column label="状态" width="180" align="center">
          <template #default="{ row }">
            <span :class="['status-tag', getStatusClass(row.status)]">
              {{ row.status }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="时限" width="120" align="center">
          <template #default="{ row }">
            <span
              :class="[
                'time-limit-tag',
                getTimeLimitInfo(row.days || 0, row.status).class,
              ]"
            >
              {{ getTimeLimitInfo(row.days || 0, row.status).text }}
            </span>
            <div
              v-if="row.delay"
              class="mt-[4px] px-[8px] py-[2px] bg-gray-200 rounded-[8px] text-[13px] cursor-pointer hover:bg-gray-300 transition-colors"
              @click="handleViewExtension(row)"
            >
              延时{{ row.delay || 0 }}天
            </div>
          </template>
        </el-table-column>

        <!-- 固定右侧操作列 -->
        <el-table-column label="操作" width="120" fixed="right" align="center">
          <template #default="{ row }">
            <div class="flex-center-center">
              <div
                class="pointer blue2"
                @click="handleOperation(row, 'detail')"
              >
                查看详情
              </div>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页器 -->
      <div class="mt-[16px] flex justify-end">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          :total="pagination.total"
          layout="total, prev, pager, next"
          @current-change="handleCurrentChange"
        />
      </div>
    </section>

    <!-- 查看延期弹窗 -->
    <el-dialog
      v-model="extensionDialogVisible"
      title="查看延期"
      width="480px"
      class="custom-dialog"
      :before-close="handleCloseExtensionDialog"
    >
      <el-form
        :model="extensionForm"
        label-position="top"
        class="extension-form"
      >
        <el-form-item label="延期时间">
          <div class="flex items-center gap-2">
            <el-input
              v-model="extensionForm.days"
              placeholder="请输入天数"
              style="width: 80px"
              disabled
            />
            <span class="text-gray-600">天</span>
          </div>
        </el-form-item>

        <el-form-item>
          <template #label>
            <span class="attachment-label">附件</span>
          </template>
          <div class="upload-area">
            <!-- 文件列表 -->
            <div v-if="fileList.length > 0" class="file-list">
              <div v-for="file in fileList" :key="file.id" class="file-item">
                <img :src="
                    file.type === 'image'
                      ? imageIcon
                      : file.type === 'pdf'
                        ? pdfIcon
                        : wordIcon
                  " />
                <span class="file-name">{{ file.name }}</span>

                <!-- 查看模式显示下载按钮 -->
                <el-icon class="download-icon" @click="downloadAttachment(file)">
                  <Download />
                </el-icon>
              </div>
            </div>

            <!-- 没有文件时的提示 -->
            <div v-if="fileList.length === 0" class="no-files-tip">
              暂无附件
            </div>
          </div>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="handleCloseExtensionDialog">返回</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
.hidden-label {
  :deep(.el-form-item__label) {
    display: none;
  }
}

.whitespace-pre-line {
  white-space: pre-line;
  font-size: 16px;
  line-height: 24px;
}

// 延期弹窗样式
.extension-form {
  .attachment-label {
    font-size: 16px;
    font-weight: 600;
    color: var(--grey1);
  }

  .upload-area {
    .file-list {
      display: flex;
      flex-direction: column;
      gap: 12px;

      .file-item {
        display: flex;
        align-items: center;
        padding: 12px;
        background-color: #f8f9fa;
        border-radius: 8px;
        border: 1px solid #e9ecef;

        img {
          width: 24px;
          height: 24px;
          margin-right: 12px;
        }

        .file-name {
          flex: 1;
          font-size: 14px;
          color: #333;
          word-break: break-all;
        }

        .download-icon {
          margin-left: 12px;
          cursor: pointer;
          color: #1890ff;
          font-size: 16px;

          &:hover {
            color: #40a9ff;
          }
        }
      }
    }

    .no-files-tip {
      text-align: center;
      color: #999;
      font-size: 14px;
      padding: 20px;
      background-color: #f8f9fa;
      border-radius: 6px;
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
